{"jsonrpc": "2.0", "error": {"code": -32603, "message": "Database connection timeout after 30 seconds", "data": {"stacktrace": ["Traceback (most recent call last):\n", "  File \"E:\\midlogic\\erp_py\\demo_error_responses.py\", line 60, in create_sample_exception\n    level_1()\n    ~~~~~~~^^\n", "  File \"E:\\midlogic\\erp_py\\demo_error_responses.py\", line 57, in level_1\n    level_2()\n    ~~~~~~~^^\n", "  File \"E:\\midlogic\\erp_py\\demo_error_responses.py\", line 54, in level_2\n    level_3()\n    ~~~~~~~^^\n", "  File \"E:\\midlogic\\erp_py\\demo_error_responses.py\", line 51, in level_3\n    raise ValueError(\"Database connection timeout after 30 seconds\")\n", "ValueError: Database connection timeout after 30 seconds\n"], "exception_type": "ValueError"}}, "id": "auto-detect-123"}