{"jsonrpc": "2.0", "error": {"code": -32603, "message": "User authentication failed", "data": {"stacktrace": ["  File \"E:\\midlogic\\erp_py\\demo_error_responses.py\", line 275, in <module>\n    sys.exit(main())\n", "  File \"E:\\midlogic\\erp_py\\demo_error_responses.py\", line 247, in main\n    demo_jsonrpc_errors()\n", "  File \"E:\\midlogic\\erp_py\\demo_error_responses.py\", line 75, in demo_jsonrpc_errors\n    basic_error = JsonRpcError.internal_error(\n", "  File \"E:\\midlogic\\erp_py\\erp\\http\\core\\jsonrpc.py\", line 158, in internal_error\n    return cls.create_error(cls.INTERNAL_ERROR, message, data, request_id, include_stacktrace, exception)\n"]}}, "id": "req-12345"}