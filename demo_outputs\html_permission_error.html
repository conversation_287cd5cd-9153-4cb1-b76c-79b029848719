
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #333333;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-header {
            border-bottom: 2px solid #dc3545;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .error-title {
            color: #dc3545;
            font-size: 2.5em;
            margin: 0 0 10px 0;
            font-weight: 300;
        }
        .error-type {
            color: #6c757d;
            font-size: 1.2em;
            margin: 0;
            font-weight: 400;
        }
        .error-message {
            background-color: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 20px;
            margin: 20px 0;
            font-size: 1.1em;
            border-radius: 0 4px 4px 0;
        }
        .stacktrace-section, .request-info-section {
            margin-top: 30px;
            border-top: 1px solid #e0e0e0;
            padding-top: 20px;
        }
        .stacktrace-section h3, .request-info-section h3 {
            color: #495057;
            font-size: 1.3em;
            margin-bottom: 15px;
        }
        .stacktrace {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', Consolas, monospace;
            font-size: 0.9em;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .request-info {
            list-style: none;
            padding: 0;
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
        }
        .request-info li {
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .request-info li:last-child {
            border-bottom: none;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-header">
            <h1 class="error-title">Access Denied</h1>
            <p class="error-type">PermissionError</p>
        </div>
        
        <div class="error-message">
            Access denied: insufficient privileges
        </div>
        
        
            <div class="request-info-section">
                <h3>Request Information</h3>
                <ul class="request-info">
                    <li><strong>Method:</strong> POST</li><li><strong>URL:</strong> http://localhost:8000/admin/settings</li><li><strong>User:</strong> <EMAIL></li><li><strong>Role:</strong> User</li>
                </ul>
            </div>
            
        
        
        
        <div class="footer">
            <p>ERP System - Error occurred at 2025-07-26 18:44:49.922454</p>
        </div>
    </div>
</body>
</html>
        