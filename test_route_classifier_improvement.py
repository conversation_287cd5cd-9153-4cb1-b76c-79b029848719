#!/usr/bin/env python3
"""
Test script to verify the RouteClassifier improvements work correctly.
This script tests that the RouteClassifier now uses SystemRouteRegistry
instead of hardcoded routes.
"""

import sys
import os
import asyncio

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from erp.utils.middleware.database import RouteClassifier
from erp.http.registries import get_system_route_registry
from erp.http.interfaces import RouteInfo, RouteScope
from erp.http.metadata import RouteType, AuthType


async def test_route_classifier_with_registry():
    """Test that RouteClassifier uses SystemRouteRegistry correctly."""
    print("🧪 Testing RouteClassifier with SystemRouteRegistry...")

    # Import route modules to register the @systemRoute decorated functions
    print("📦 Importing route modules to register system routes...")
    try:
        import erp.routes.system
        import erp.routes.database
        print("✅ Route modules imported successfully")
    except Exception as e:
        print(f"⚠️  Warning: Could not import route modules: {e}")

    # Clear any cached routes to ensure fresh test
    RouteClassifier.clear_cache()

    # Get the system registry
    system_registry = get_system_route_registry()

    # Debug: Check what's actually in the registry
    print(f"\n🔍 Debug: Registry routes: {list(system_registry._routes.keys())}")
    print(f"🔍 Debug: Registry lookup: {list(system_registry._route_lookup.keys())}")

    # Test 1: Check that system routes are loaded from registry
    print("\n📋 Test 1: System routes from registry")
    system_routes = RouteClassifier._get_system_routes()
    print(f"System routes: {sorted(system_routes)}")
    
    # Test 2: Test known system routes
    print("\n📋 Test 3: Testing known system routes")
    test_routes = [
        ("/", False, "Root route should skip middleware"),
        ("/health", False, "Health route should skip middleware"),
        ("/databases", False, "Database list should skip middleware"),
        ("/docs", False, "FastAPI docs should skip middleware"),
        ("/openapi.json", False, "OpenAPI spec should skip middleware"),
        ("/redoc", False, "ReDoc should skip middleware"),
        ("/favicon.ico", False, "Favicon should skip middleware"),
        ("/test-direct", True, "Test direct route should need middleware (not registered)"),
        ("/static/css/style.css", False, "Static files should skip middleware"),
        ("/api/databases", False, "Database API should skip middleware"),
        ("/home", True, "Home route should need middleware"),
        ("/some/addon/route", True, "Addon routes should need middleware"),
    ]
    
    for path, expected_needs_middleware, description in test_routes:
        needs_middleware = RouteClassifier.needs_database_middleware(path)
        status = "✅" if needs_middleware == expected_needs_middleware else "❌"
        print(f"{status} {path}: {description} (needs_middleware={needs_middleware})")
        
        if needs_middleware != expected_needs_middleware:
            print(f"   Expected: {expected_needs_middleware}, Got: {needs_middleware}")
    
    # Test 4: Test caching behavior
    print("\n📋 Test 4: Testing caching behavior")
    routes_1 = RouteClassifier._get_system_routes()
    routes_2 = RouteClassifier._get_system_routes()
    print(f"Cache working: {routes_1 is routes_2}")
    
    # Test 5: Test cache clearing
    print("\n📋 Test 5: Testing cache clearing")
    RouteClassifier.clear_cache()
    routes_3 = RouteClassifier._get_system_routes()
    print(f"Cache cleared: {routes_1 is not routes_3}")
    
    print("\n✅ All tests completed!")


async def test_fallback_behavior():
    """Test fallback behavior when registry is not available."""
    print("\n🧪 Testing fallback behavior...")
    
    # Clear cache first
    RouteClassifier.clear_cache()
    
    # Temporarily break the registry import to test fallback
    original_get_registry = RouteClassifier._get_system_routes.__func__
    
    def mock_failing_get_routes(cls):
        """Mock function that simulates registry failure."""
        if cls._cached_system_routes is None:
            # Simulate failure and use fallback
            cls._cached_system_routes = {
                "/", "/health", "/docs", "/openapi.json", "/redoc", "/favicon.ico",
                "/databases", "/databases/", "/test-direct"
            }
        return cls._cached_system_routes
    
    # Test that fallback routes still work
    RouteClassifier._get_system_routes = classmethod(mock_failing_get_routes)
    
    fallback_routes = RouteClassifier._get_system_routes()
    print(f"Fallback routes: {sorted(fallback_routes)}")
    
    # Test that basic routes still work with fallback
    test_cases = [
        ("/", False),
        ("/health", False),
        ("/home", True),
    ]
    
    for path, expected in test_cases:
        result = RouteClassifier.needs_database_middleware(path)
        status = "✅" if result == expected else "❌"
        print(f"{status} Fallback test {path}: {result == expected}")
    
    print("✅ Fallback behavior test completed!")


async def main():
    """Main test function."""
    print("🚀 Starting RouteClassifier improvement tests...\n")
    
    try:
        await test_route_classifier_with_registry()
        await test_fallback_behavior()
        print("\n🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
