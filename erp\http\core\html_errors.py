"""
HTML error response generator for HTTP routes
Creates minimal white background error pages with detailed error information
"""

import traceback
import html
from typing import Optional, Dict, Any
from fastapi.responses import HTMLResponse


class HTMLErrorResponse:
    """Generator for HTML error responses"""
    
    @staticmethod
    def create_error_page(
        error: Exception,
        status_code: int = 500,
        title: Optional[str] = None,
        include_stacktrace: bool = True,
        request_info: Optional[Dict[str, Any]] = None
    ) -> HTMLResponse:
        """
        Create an HTML error page
        
        Args:
            error: The exception that occurred
            status_code: HTTP status code
            title: Custom title for the error page
            include_stacktrace: Whether to include stacktrace
            request_info: Additional request information to display
            
        Returns:
            HTMLResponse with error page
        """
        error_title = title or f"Error {status_code}"
        error_message = str(error) or "An error occurred"
        error_type = type(error).__name__
        
        # Generate stacktrace if requested
        stacktrace_html = ""
        if include_stacktrace:
            stacktrace_lines = traceback.format_exception(
                type(error), error, error.__traceback__
            )
            stacktrace_text = ''.join(stacktrace_lines)
            stacktrace_html = f"""
            <div class="stacktrace-section">
                <h3>Stack Trace</h3>
                <pre class="stacktrace">{html.escape(stacktrace_text)}</pre>
            </div>
            """
        
        # Generate request info if provided
        request_info_html = ""
        if request_info:
            request_items = []
            for key, value in request_info.items():
                escaped_key = html.escape(str(key))
                escaped_value = html.escape(str(value))
                request_items.append(f"<li><strong>{escaped_key}:</strong> {escaped_value}</li>")
            
            request_info_html = f"""
            <div class="request-info-section">
                <h3>Request Information</h3>
                <ul class="request-info">
                    {''.join(request_items)}
                </ul>
            </div>
            """
        
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{html.escape(error_title)}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #333333;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .error-header {{
            border-bottom: 2px solid #dc3545;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .error-title {{
            color: #dc3545;
            font-size: 2.5em;
            margin: 0 0 10px 0;
            font-weight: 300;
        }}
        .error-type {{
            color: #6c757d;
            font-size: 1.2em;
            margin: 0;
            font-weight: 400;
        }}
        .error-message {{
            background-color: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 20px;
            margin: 20px 0;
            font-size: 1.1em;
            border-radius: 0 4px 4px 0;
        }}
        .stacktrace-section, .request-info-section {{
            margin-top: 30px;
            border-top: 1px solid #e0e0e0;
            padding-top: 20px;
        }}
        .stacktrace-section h3, .request-info-section h3 {{
            color: #495057;
            font-size: 1.3em;
            margin-bottom: 15px;
        }}
        .stacktrace {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', Consolas, monospace;
            font-size: 0.9em;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }}
        .request-info {{
            list-style: none;
            padding: 0;
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
        }}
        .request-info li {{
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }}
        .request-info li:last-child {{
            border-bottom: none;
        }}
        .footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="error-header">
            <h1 class="error-title">{html.escape(error_title)}</h1>
            <p class="error-type">{html.escape(error_type)}</p>
        </div>
        
        <div class="error-message">
            {html.escape(error_message)}
        </div>
        
        {request_info_html}
        
        {stacktrace_html}
        
        <div class="footer">
            <p>ERP System - Error occurred at {html.escape(str(__import__('datetime').datetime.now()))}</p>
        </div>
    </div>
</body>
</html>
        """
        
        return HTMLResponse(content=html_content, status_code=status_code)
    
    @staticmethod
    def create_simple_error_page(
        message: str,
        status_code: int = 500,
        title: Optional[str] = None
    ) -> HTMLResponse:
        """
        Create a simple HTML error page without stacktrace
        
        Args:
            message: Error message to display
            status_code: HTTP status code
            title: Custom title for the error page
            
        Returns:
            HTMLResponse with simple error page
        """
        error_title = title or f"Error {status_code}"
        
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{html.escape(error_title)}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #333333;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }}
        .container {{
            max-width: 600px;
            text-align: center;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .error-title {{
            color: #dc3545;
            font-size: 3em;
            margin: 0 0 20px 0;
            font-weight: 300;
        }}
        .error-message {{
            font-size: 1.2em;
            color: #495057;
            margin-bottom: 30px;
        }}
        .footer {{
            color: #6c757d;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="error-title">{html.escape(error_title)}</h1>
        <p class="error-message">{html.escape(message)}</p>
        <div class="footer">
            <p>ERP System</p>
        </div>
    </div>
</body>
</html>
        """
        
        return HTMLResponse(content=html_content, status_code=status_code)


def create_http_error_response(
    error: Exception,
    status_code: int = 500,
    request_info: Optional[Dict[str, Any]] = None,
    include_stacktrace: bool = True
) -> HTMLResponse:
    """
    Convenience function to create HTTP error response
    
    Args:
        error: The exception that occurred
        status_code: HTTP status code
        request_info: Additional request information
        include_stacktrace: Whether to include stacktrace
        
    Returns:
        HTMLResponse with error page
    """
    return HTMLErrorResponse.create_error_page(
        error=error,
        status_code=status_code,
        request_info=request_info,
        include_stacktrace=include_stacktrace
    )
